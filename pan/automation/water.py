import asyncio

from caspia.pan import get_global_pan
from caspia.pan import reactive as r
from caspia.toolbox.reactive.record import record

from ..utils import utery

pan = get_global_pan()
automator = pan.automator
temp = pan.lookup['temperature-sensor']
outlet = pan.lookup.outlet
light = pan.lookup.light
thermostat = pan.lookup.thermostat

sviti_nekde = light('prizemi.technicka.hlavni').is_on | \
            light('prizemi.kuchyn.linka').is_on | \
            light('prizemi.pracovna.hlavni').is_on | \
            light('prizemi.spize.hlavni').is_on | \
            light('prizemi.chodba.hlavni0').is_on | \
            light('prizemi.chodba.hlavni1').is_on | \
            light('prizemi.chodba.hlavni2').is_on | \
            light('prizemi.chodba.hlavni3').is_on | \
            light('prizemi.komora.hlavni').is_on | \
            light('prizemi.koupelna.hlavni1').is_on | \
            light('prizemi.koupelna.hlavni2').is_on | \
            light('prizemi.koupelna.hlavni3').is_on | \
            light('prizemi.koupelna.hlavni4').is_on | \
            light('prizemi.kuchyn.hlavni1').is_on | \
            light('prizemi.kuchyn.hlavni2').is_on | \
            light('prizemi.kuchyn.hlavni3').is_on | \
            light('prizemi.kuchyn.hlavni4').is_on | \
            light('prizemi.kuchyn.hlavni5').is_on | \
            light('prizemi.kuchyn.hlavni6').is_on | \
            light('prizemi.kuchyn.stul').is_on | \
            light('prizemi.kuchyn.stul_nahoru').is_on | \
            light('prizemi.loznice.hlavni1').is_on | \
            light('prizemi.loznice.hlavni2').is_on | \
            light('prizemi.loznice.hlavni3').is_on | \
            light('prizemi.loznice.hlavni4').is_on | \
            light('prizemi.obyvak.hlavni1').is_on | \
            light('prizemi.obyvak.hlavni2').is_on | \
            light('prizemi.obyvak.hlavni3').is_on | \
            light('prizemi.obyvak.hlavni4').is_on | \
            light('prizemi.pokoj_vlevo.hlavni1').is_on | \
            light('prizemi.pokoj_vlevo.hlavni2').is_on | \
            light('prizemi.pokoj_vlevo.hlavni3').is_on | \
            light('prizemi.pokoj_vlevo.hlavni4').is_on | \
            light('prizemi.pokoj_vpravo.hlavni1').is_on | \
            light('prizemi.pokoj_vpravo.hlavni2').is_on | \
            light('prizemi.pokoj_vpravo.hlavni3').is_on | \
            light('prizemi.pokoj_vpravo.hlavni4').is_on | \
            light('prizemi.pradelna.hlavni1').is_on | \
            light('prizemi.pradelna.hlavni2').is_on | \
            light('prizemi.wc.hlavni').is_on | \
            light('prizemi.zadveri.hlavni1').is_on | \
            light('prizemi.zadveri.hlavni2').is_on


@automator.create_automation('čerpadlo TUV, když je někdo doma, nebo se svítí tak udržuj teplotu')
@automator.on_update(r.someone_present() &
                     (r.time_between('6:00', '18:59') | (sviti_nekde & r.time_between('19:00', '5:59'))))
async def topeni_tuv(value, **kwargs):
    if value:
        await thermostat('tuv.termostat').target_state.write('heating')
    else:
        await thermostat('tuv.termostat').target_state.write('off')


@automator.create_automation('minimální teplota pro čerpadlo TUV')
@automator.on_update(r.time_between('7:00', '23:00'))
async def tuv_temperature_set(value, **kwargs):
    if value:
        await thermostat('tuv.termostat').target_temp.write(33.0)
    else:
        await thermostat('tuv.termostat').target_temp.write(15.0)


@automator.create_automation('čerpadlo TUV, sepnutí jednou týdně')
@automator.when(r.transition(r.time_is('10:00') & utery & r.everyone_away()) == (False, True))
async def tuv_udrzba():
    await outlet('prizemi.technicka.cerpadlo_tuv').is_on.write(True)
