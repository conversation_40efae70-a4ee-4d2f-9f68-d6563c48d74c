from functools import partial
from caspia.pan import reactive as r

# reaktivní min a max
rmin = partial(r.lambda_, min)
rmax = partial(r.lambda_, max)

# může nastávat opakovaně
venku_tma = r.time_between(r.sunset(), '23:59') | r.time_between('0:00', r.sunrise())
setmelo_se = r.at_sunset()
rozednilo_se = r.at_sunrise()

# vítr
# fouká tak, že bouchají žaluzie a můžou budit při spánku
venku_fouka1 = (14.0 <= rmax(r.weather_current('wind_gust'), r.weather_upcoming('wind_gust')))
# fouk<PERSON> tak, že už to může poškodit žaluzie
venku_fouka2 = (18.0 <= rmax(r.weather_current('wind_gust'), r.weather_upcoming('wind_gust')))
