version: '1'
modules:
    meadow: # Meadow is the central communication node
    influxdb: # Time-series database for historical datapoints
    grafana: # Data visualization web interface
    gateway: # Caspia's gateway
        local-can-id: 0x1F0 # The CAN ID the gateway will
                            # use on the CAN BUS
    pan: # PAN is the automation module
        environment:
            CSP_DARKSKY_KEY: dc094643eb2aed1f5ba7c9a0bca57834
            #CSP_TELEGRAM_ACCESS_TOKEN: '**********************************************'
            #CSP_TELEGRAM_GROUP_ID: '-786544003'
    presence-arp:
        presence-name: dum.pritomnost
        timeout: 600
        interval: 10
        config: presence-arp/presence.yaml
    homeserver: # API for GUI (login:pass)
        login: caspia:fZKAmMDa9iDfv7X
    deconz-app:
    deconz-gateway:
        password: l<PERSON><PERSON><PERSON><PERSON>