import os
import asyncio

from caspia.pan import get_global_pan
# setup recording meadow-characteristics
from caspia.toolbox.integrations.influxdb import InfluxDBConnector
# setup recording of observables
from caspia.toolbox.reactive import record

pan = get_global_pan()

connector = InfluxDBConnector(pan.consumer_conn, url=os.environ['CSP_METRICS_INFLUXDB_URL'])
asyncio.ensure_future(connector.start(), loop=pan.loop)

record.register_store(record.InfluxDBStore(url=os.environ['CSP_METRICS_INFLUXDB_URL']))
