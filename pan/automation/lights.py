import asyncio
import time

from caspia.pan import get_global_pan
from caspia.pan import reactive as r

pan = get_global_pan()
automator = pan.automator
namespace = pan.lookup.namespace
button = pan.lookup.button
light = pan.lookup.light
outlet = pan.lookup.outlet
door = pan.lookup.door
thermostat = pan.lookup.thermostat
fan = pan.lookup.fan
sun = r.get_sun()
from .reactive import venku_tma
from caspia.reactive import prolong

timer = r.Value(0)
suteren_timer_end = r.Value(9223372036854775807)

hodina_pred_zachodem = r.lambda_(lambda t: f"{(t[0] - 1) % 24:02d}:{t[1]:02d}", r.sunset())
hodina_po_zachodu    = r.lambda_(lambda t: f"{(t[0] + 1) % 24:02d}:{t[1]:02d}", r.sunset())

hodina_pred_vychodem = r.lambda_(lambda t: f"{(t[0] - 1) % 24:02d}:{t[1]:02d}", r.sunrise())
hodina_po_vychodu    = r.lambda_(lambda t: f"{(t[0] + 1) % 24:02d}:{t[1]:02d}", r.sunrise())

with namespace('prizemi.koupelna'):
    button('.u_vstupu.vlevo').on('click').do(light('.wc').toggle)
    button('.u_vstupu.vpravo').on('click').do(light('.hlavni').toggle)
    button('.u_vstupu.vpravo').on('hold').do(outlet('.topny_zebrik').is_on, True)

with namespace('patro.koupelna'):
    button('.u_vstupu.vlevo').on('click').do(light('.wc').toggle)
    button('.u_vstupu.vpravo').on('click').do(light('.hlavni').toggle)
    button('.u_vstupu.vpravo').on('hold').do(outlet('.topny_zebrik').is_on, True)

with namespace('prizemi.loznice'):
    button('.u_vstupu.vpravo').on('click').do(light('.hlavni').toggle)
    button('.postel_vlevo.vpravo').on('click').do(light('.hlavni').toggle)
    button('.postel_vlevo.vlevo').on('click').do(light('.hlavni').toggle)

with namespace('prizemi.obyvak'):
    button('.u_vstupu.levy').on('click').do(light('..chodba.hlavni').toggle)
    button('.u_vstupu.1').on('click').do(light('..kuchyn.hlavni').toggle)
    #button('.u_vstupu.1').on('hold').do(light('.drevnik_led').toggle)
    button('.u_vstupu.2').on('click').do(light('..kuchyn.linka').toggle)
    #button('.u_vstupu.2').on('hold').do(light('.kuchyn_nad_skrinkou').toggle)
    #button('.u_vstupu.3').on('click').do(outlet('..kuchyn.nad_hornima_skrinkama').toggle)
    button('.u_vstupu.4').on('click').do(light('.hlavni').toggle)       # TODO led pásek

with namespace('prizemi.kuchyn'):
    button('.drez_vpravo.pravy').on('click').do(light('.linka').toggle)
    button('.drez_vlevo.levy').on('click').do(light('.linka').toggle)

with namespace('prizemi.technicka'):
    button('.u_vstupu.levy').on('click').do(light('.sklep').toggle)
    button('.u_vstupu.pravy').on('click').do(light('.hlavni').toggle)

async def prichodove_tlacitko():
    # TODO manuální přepnutí jako host doma?
    a = 1


async def odchodove_tlacitko():
    # TODO počkat nějakou dobu?
    await light('dum.group.vse').is_on.write(False)

with namespace('prizemi.zadveri'):
    button('.u_vstupu.levy').on('hold', 1.8).do(prichodove_tlacitko)
    button('.u_vstupu.pravy').on('click').do(light('.hlavni').toggle)
    button('.u_vstupu.pravy').on('hold', 1.8).do(odchodove_tlacitko)

    button('.u_vstupu.levy').on('click').do(light('.hlavni').toggle)    # TODO bude světlo vchod
    button('.u_vstupu.pravy').on('click').do(light('.hlavni').toggle)
    button('.u_chodby.levy').on('click').do(light('.hlavni').toggle)
    button('.u_chodby.pravy').on('click').do(light('..chodba.hlavni').toggle)

with namespace('prizemi.chodba'):
    button('.u_vstupu.levy').on('click').do(light('..zadveri.hlavni').toggle)
    button('.u_vstupu.pravy').on('click').do(light('.hlavni').toggle)
    button('.u_schodu.levy').on('click').do(light('.hlavni').toggle)
    button('.u_schodu.levy').on('hold').do(light('.schody').toggle)
    button('.u_loznice.levy').on('click').do(light('.hlavni').toggle)

with namespace('prizemi.sauna'):
    button('.u_vstupu.levy').on('click').do(light('.hlavni').toggle) # TODO led
    button('.u_vstupu.pravy').on('click').do(light('.hlavni').toggle)
    button('.u_vstupu.pravy').on('hold').do(light('..koupelna.hlavni').toggle)

with namespace('patro.loznice'):
    button('.u_vstupu.levy').on('click').do(light('.hlavni').toggle)
    button('.postel_vlevo.pravy').on('click').do(light('.hlavni').toggle)
    button('.postel_vpravo.levy').on('click').do(light('.hlavni').toggle)

with namespace('patro.detsky_pokoj'):
    button('.u_vstupu.pravy').on('click').do(light('.u_okna').toggle)
    button('.u_vstupu.levy').on('click').do(light('.hlavni').toggle)
    button('.za_rohem.pravy').on('click').do(light('.u_okna').toggle)

with namespace('patro.pracovna'):
    button('.u_vstupu.pravy').on('click').do(light('.hlavni').toggle)

with namespace('patro.chodba'):
    button('.u_vstupu.levy').on('click').do(light('.hlavni').toggle)
    button('.u_vstupu.levy').on('hold').do(light('prizemi.chodba.schody').toggle)
    button('.u_vstupu.pravy').on('click').do(light('prizemi.chodba.hlavni').toggle)
    button('.u_koupelny.levy').on('click').do(light('.hlavni').toggle)
    button('.u_koupelny.levy').on('hold').do(light('.led').toggle)
    button('.u_pokoje.levy').on('click').do(light('.hlavni').toggle)

with namespace('patro.puda'):
    button('.u_vstupu.levy').on('click').do(light('.hlavni').toggle)


# PIR patro chodba u pracovny
pohyb_patro_chodba_u_pracovny = prolong(button('patro.chodba.detekce_pohybu_u_pracovny').is_pushed, min_duration=20)
svitit_patro_chodba_u_pracovny = pohyb_patro_chodba_u_pracovny & venku_tma

@automator.create_automation('PIR patro chodba u pracovny: Rozsvit/Zhasni v predsini podle PIR')
@automator.on_update(svitit_patro_chodba_u_pracovny)
async def pohyb_patro_chodba_u_pracovny_zapni(value, **kwargs):
    await light('prizemi.chodba.schody').is_on.write(value)

@automator.create_automation('PIR patro chodba u pracovny: Udržuj světlo v předsíni zapnuté když sensor detekuje pohyb')
@automator.when(svitit_patro_chodba_u_pracovny & r.every(sec=10))
async def aaa1(*args, **kwargs):
    await light('prizemi.chodba.schody').is_on.write(True)


# PIR patro chodba u loznice
pohyb_patro_chodba_u_loznice = prolong(button('patro.chodba.detekce_pohybu_u_loznice').is_pushed, min_duration=60)
svitit_patro_chodba_u_loznice = pohyb_patro_chodba_u_loznice & venku_tma

@automator.create_automation('PIR patro chodba u loznice: Rozsvit/Zhasni v predsini podle PIR')
@automator.on_update(svitit_patro_chodba_u_loznice)
async def pohyb_patro_chodba_u_loznice_zapni(value, **kwargs):
    await light('patro.chodba.led').is_on.write(value)

@automator.create_automation('PIR patro chodba u loznice: Udržuj světlo v předsíni zapnuté když sensor detekuje pohyb')
@automator.when(svitit_patro_chodba_u_loznice & r.every(sec=10))
async def aaa2(*args, **kwargs):
    await light('patro.chodba.led').is_on.write(True)


# PIR prizemi chodba
pohyb_prizemi_chodba = prolong(button('prizemi.chodba.detekce_pohybu').is_pushed, min_duration=60)
svitit_prizemi_chodba = pohyb_prizemi_chodba & venku_tma

@automator.create_automation('PIR prizemi chodba: Rozsvit/Zhasni v predsini podle PIR')
@automator.on_update(svitit_prizemi_chodba)
async def pohyb_prizemi_chodba_zapni(value, **kwargs):
    await light('prizemi.chodba.led').is_on.write(value)

@automator.create_automation('PIR prizemi chodba: Udržuj světlo v předsíni zapnuté když sensor detekuje pohyb')
@automator.when(svitit_prizemi_chodba & r.every(sec=10))
async def aaa3(*args, **kwargs):
    await light('prizemi.chodba.led').is_on.write(True)

auto_temp_lights = [
    light('prizemi.chodba.1.tmp'),
    light('prizemi.chodba.2.tmp'),
    light('prizemi.kuchyn.hlavni.1.tmp'),
    light('prizemi.kuchyn.hlavni.2.tmp'),
    light('prizemi.chodba.1.tmp'),
    light('prizemi.chodba.2.tmp'),
    light('patro.chodba.1.tmp'),
    light('patro.chodba.2.tmp'),
    light('patro.chodba.3.tmp'),
    light('prizemi.koupelna.hlavni.1.tmp'),
    light('prizemi.koupelna.hlavni.2.tmp'),
    light('prizemi.koupelna.hlavni.3.tmp'),
    light('patro.koupelna.1.tmp'),
    light('patro.koupelna.2.tmp'),
    light('patro.koupelna.3.tmp'),
    light('patro.loznice.hlavni.1.tmp'),
    light('patro.loznice.hlavni.2.tmp'),
]


async def set_all_lights(brightness: float = 0.6, color_temp: int = 450) -> None:
    """
    Nastaví jas (0–1) a teplotu (0 je bílé světlo, 450 je plně žluté) pro všechna světla.
    """
    tasks = []
    for l in auto_temp_lights:
        tasks.append(l['brightness'].write(float(brightness)))
        tasks.append(l['color_temperature'].write(int(color_temp)))
    await asyncio.gather(*tasks)

async def svetla_soumrak():
    await set_all_lights(0.6, 450)



WARM = 450  # teplé bílé (min. teplota)
COOL = 0    # studené bílé (max. teplota)

ORANGE_CT = 360  # #FF4000
RED_CT = 450     # #FF0000


# --- SOUMRAK / ÚSVIT ---
@pan.builder.create_switch_with_action('switch.rezim_soumrak')
async def rezim_soumrak():
    await asyncio.sleep(0.3)
    await asyncio.gather(*[
        # chodby: #FF4000, 100 % (CT=360)
        light('prizemi.chodba.1.tmp').brightness.write(1.0),
        light('prizemi.chodba.1.tmp').color_temperature.write(ORANGE_CT),
        light('prizemi.chodba.2.tmp').brightness.write(1.0),
        light('prizemi.chodba.2.tmp').color_temperature.write(ORANGE_CT),
        #light('patro.chodba.1.tmp').brightness.write(1.0),
        #light('patro.chodba.1.tmp').color_temperature.write(ORANGE_CT),
        light('patro.chodba.2.tmp').brightness.write(1.0),
        light('patro.chodba.2.tmp').color_temperature.write(ORANGE_CT),
        #light('patro.chodba.3.tmp').brightness.write(1.0),
        #light('patro.chodba.3.tmp').color_temperature.write(ORANGE_CT),

        # kuchyně hlavní: min. teplota, 60 %
        light('prizemi.kuchyn.hlavni.1.tmp').brightness.write(0.6),
        light('prizemi.kuchyn.hlavni.1.tmp').color_temperature.write(WARM),
        light('prizemi.kuchyn.hlavni.2.tmp').brightness.write(0.6),
        light('prizemi.kuchyn.hlavni.2.tmp').color_temperature.write(WARM),

        # koupelny: min. teplota, 60 %
        light('prizemi.koupelna.hlavni.1.tmp').brightness.write(0.6),
        light('prizemi.koupelna.hlavni.1.tmp').color_temperature.write(WARM),
        light('prizemi.koupelna.hlavni.2.tmp').brightness.write(0.6),
        light('prizemi.koupelna.hlavni.2.tmp').color_temperature.write(WARM),
        light('prizemi.koupelna.hlavni.3.tmp').brightness.write(0.6),
        light('prizemi.koupelna.hlavni.3.tmp').color_temperature.write(WARM),
        light('patro.koupelna.1.tmp').brightness.write(0.6),
        light('patro.koupelna.1.tmp').color_temperature.write(WARM),
        light('patro.koupelna.2.tmp').brightness.write(0.6),
        light('patro.koupelna.2.tmp').color_temperature.write(WARM),
        light('patro.koupelna.3.tmp').brightness.write(0.6),
        light('patro.koupelna.3.tmp').color_temperature.write(WARM),

        # ložnice (patro): #FF0000, 20 % (CT=450)
        light('patro.loznice.hlavni.1.tmp').brightness.write(0.2),
        light('patro.loznice.hlavni.1.tmp').color_temperature.write(RED_CT),
        light('patro.loznice.hlavni.2.tmp').brightness.write(0.2),
        light('patro.loznice.hlavni.2.tmp').color_temperature.write(RED_CT),
    ])

# --- DEN ---
@pan.builder.create_switch_with_action('switch.rezim_den')
async def rezim_den():
    await asyncio.sleep(0.3)
    await asyncio.gather(*[
        # chodby: #FFFFFF, 80 % (CT=0)
        light('prizemi.chodba.1.tmp').brightness.write(0.8),
        light('prizemi.chodba.1.tmp').color_temperature.write(COOL),
        light('prizemi.chodba.2.tmp').brightness.write(0.8),
        light('prizemi.chodba.2.tmp').color_temperature.write(COOL),
        #light('patro.chodba.1.tmp').brightness.write(0.8),
        #light('patro.chodba.1.tmp').color_temperature.write(COOL),
        light('patro.chodba.2.tmp').brightness.write(0.8),
        light('patro.chodba.2.tmp').color_temperature.write(COOL),
        #light('patro.chodba.3.tmp').brightness.write(0.8),
        #light('patro.chodba.3.tmp').color_temperature.write(COOL),

        # kuchyně hlavní: max. teplota, 100 %
        light('prizemi.kuchyn.hlavni.1.tmp').brightness.write(1.0),
        light('prizemi.kuchyn.hlavni.1.tmp').color_temperature.write(COOL),
        light('prizemi.kuchyn.hlavni.2.tmp').brightness.write(1.0),
        light('prizemi.kuchyn.hlavni.2.tmp').color_temperature.write(COOL),

        # koupelny: max. teplota, 85 %
        light('prizemi.koupelna.hlavni.1.tmp').brightness.write(0.85),
        light('prizemi.koupelna.hlavni.1.tmp').color_temperature.write(COOL),
        light('prizemi.koupelna.hlavni.2.tmp').brightness.write(0.85),
        light('prizemi.koupelna.hlavni.2.tmp').color_temperature.write(COOL),
        light('prizemi.koupelna.hlavni.3.tmp').brightness.write(0.85),
        light('prizemi.koupelna.hlavni.3.tmp').color_temperature.write(COOL),
        light('patro.koupelna.1.tmp').brightness.write(0.85),
        light('patro.koupelna.1.tmp').color_temperature.write(COOL),
        light('patro.koupelna.2.tmp').brightness.write(0.85),
        light('patro.koupelna.2.tmp').color_temperature.write(COOL),
        light('patro.koupelna.3.tmp').brightness.write(0.85),
        light('patro.koupelna.3.tmp').color_temperature.write(COOL),

        # ložnice (patro): #FFFFFF, 80 % (CT=0)
        light('patro.loznice.hlavni.1.tmp').brightness.write(0.8),
        light('patro.loznice.hlavni.1.tmp').color_temperature.write(COOL),
        light('patro.loznice.hlavni.2.tmp').brightness.write(0.8),
        light('patro.loznice.hlavni.2.tmp').color_temperature.write(COOL),
    ])

# --- NOC ---
@pan.builder.create_switch_with_action('switch.rezim_noc')
async def rezim_noc():
    await asyncio.sleep(0.3)
    await asyncio.gather(*[
        # chodby: #FF0000, 100 % (CT=450)
        light('prizemi.chodba.1.tmp').brightness.write(1.0),
        light('prizemi.chodba.1.tmp').color_temperature.write(RED_CT),
        light('prizemi.chodba.2.tmp').brightness.write(1.0),
        light('prizemi.chodba.2.tmp').color_temperature.write(RED_CT),
        #light('patro.chodba.1.tmp').brightness.write(1.0),
        #light('patro.chodba.1.tmp').color_temperature.write(RED_CT),
        light('patro.chodba.2.tmp').brightness.write(1.0),
        light('patro.chodba.2.tmp').color_temperature.write(RED_CT),
        #light('patro.chodba.3.tmp').brightness.write(1.0),
        #light('patro.chodba.3.tmp').color_temperature.write(RED_CT),

        # kuchyně hlavní: min. teplota, 30 %
        light('prizemi.kuchyn.hlavni.1.tmp').brightness.write(0.3),
        light('prizemi.kuchyn.hlavni.1.tmp').color_temperature.write(WARM),
        light('prizemi.kuchyn.hlavni.2.tmp').brightness.write(0.3),
        light('prizemi.kuchyn.hlavni.2.tmp').color_temperature.write(WARM),

        # koupelny: min. teplota, 30 %
        light('prizemi.koupelna.hlavni.1.tmp').brightness.write(0.3),
        light('prizemi.koupelna.hlavni.1.tmp').color_temperature.write(WARM),
        light('prizemi.koupelna.hlavni.2.tmp').brightness.write(0.3),
        light('prizemi.koupelna.hlavni.2.tmp').color_temperature.write(WARM),
        light('prizemi.koupelna.hlavni.3.tmp').brightness.write(0.3),
        light('prizemi.koupelna.hlavni.3.tmp').color_temperature.write(WARM),
        light('patro.koupelna.1.tmp').brightness.write(0.3),
        light('patro.koupelna.1.tmp').color_temperature.write(WARM),
        light('patro.koupelna.2.tmp').brightness.write(0.3),
        light('patro.koupelna.2.tmp').color_temperature.write(WARM),
        light('patro.koupelna.3.tmp').brightness.write(0.3),
        light('patro.koupelna.3.tmp').color_temperature.write(WARM),

        # ložnice (patro): #FF0000, 20 % (CT=450)
        light('patro.loznice.hlavni.1.tmp').brightness.write(0.2),
        light('patro.loznice.hlavni.1.tmp').color_temperature.write(RED_CT),
        light('patro.loznice.hlavni.2.tmp').brightness.write(0.2),
        light('patro.loznice.hlavni.2.tmp').color_temperature.write(RED_CT),
    ])

    @pan.automator.create_automation('Nastal soumrak')
    @pan.automator.when(r.time_is(hodina_pred_zachodem) | r.time_is(hodina_pred_vychodem))
    async def nastal_soumrak():
        await rezim_soumrak()

    @pan.automator.create_automation('Nastala noc')
    @pan.automator.when(r.time_is(hodina_po_zachodu))
    async def nastala_noc():
        await rezim_noc()

    @pan.automator.create_automation('Nastal den')
    @pan.automator.when(r.time_is(hodina_po_vychodu))
    async def nastal_den():
        await rezim_den()