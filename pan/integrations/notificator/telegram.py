import os

from .base import NotificatorBase

class TelegramNotificator(NotificatorBase):

    def __init__(self, group_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.access_token = os.environ.get("CSP_TELEGRAM_ACCESS_TOKEN")
        self.group_id = group_id

        self.message_endpoint = f"https://api.telegram.org/bot{self.access_token}/sendMessage"

    async def raise_notification(self, message):
        payload = {
            "chat_id": self.group_id,
            "text": message
        }

        async with self._session.post(self.message_endpoint, json=payload) as response:
            status = response.status
            content = await response.text()

            self._logger.info(f"[{status}] Response from Telegram Message Endpoint: {content}")
