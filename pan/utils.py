import asyncio
from functools import partial

import caspia.pan.reactive as r
from caspia.pan import get_global_pan

pan = get_global_pan()
namespace = pan.lookup.namespace
automator = pan.automator
button = pan.lookup.button
outlet = pan.lookup.outlet
light = pan.lookup.light
blinds = pan.lookup.blinds
fan = pan.lookup.fan
thermostat = pan.lookup.thermostat
temp = pan.lookup['temperature-sensor']
humidity = pan.lookup['humidity-sensor']
co2 = pan.lookup['carbon-dioxide-sensor']
noise = pan.lookup['noise-sensor']
light_sensor = pan.lookup['light-sensor']
blindsControl = pan.lookup['blinds-control']

pondeli = r.monday
utery = r.tuesday
streda = r.wednesday
ctvrtek = r.thursday
patek = r.friday
sobota = r.saturday
nedele = r.sunday


class BlindsMode:
    manual = 'manual'
    block = 'block-direct-sun-or-down-open'


blinds_all_names = [
    'patro.detsky_pokoj.stred',
    'patro.detsky_pokoj.vlevo',
    'patro.detsky_pokoj.vpravo',
    'patro.koupelna.hlavni',
    'patro.loznice.hlavni',
    'patro.pracovna.hlavni',
    'prizemi.koupelna.hlavni',
    'prizemi.kuchyn.hlavni',
    'prizemi.loznice.hlavni',
    'prizemi.obyvak.fix',
    'prizemi.obyvak.terasa_fix',
    'prizemi.obyvak.terasa_vstup',
    'prizemi.obyvak.vpravo',
]

blinds_obyvak_names = [
    'prizemi.koupelna.hlavni',
    'prizemi.kuchyn.hlavni',
    'prizemi.obyvak.fix',
    'prizemi.obyvak.terasa_fix',
    'prizemi.obyvak.terasa_vstup',
    'prizemi.obyvak.vpravo',
]

blinds_timeout = 0.2


def blinds_control(button_up, button_down, blinds, blinds_ctl):
    button_up.on('push').do(blinds.move_up)
    button_up.on('release').do(blinds.stop_movement)
    button_down.on('push').do(blinds.move_down)
    button_down.on('release').do(blinds.stop_movement)

    # take over after being held
    was_held = False

    async def on_hold(*args, **kwargs):
        nonlocal was_held
        was_held = True

    async def on_release(direction, *args, **kwargs):
        await blinds_ctl.mode.write('manual')
        nonlocal was_held
        if was_held:
            was_held = False
            if direction == 'up':
                await asyncio.sleep(0.2)
                await blinds.move_up()
            else:
                await asyncio.sleep(0.2)
                await blinds.move_down()

    button_up.on('hold', 1.9).do(on_hold)
    button_down.on('hold', 1.9).do(on_hold)
    button_up.on('release').do(partial(on_release, 'up'))
    button_down.on('release').do(partial(on_release, 'down'))


# async def turn_on(name):
#     # print(get_time_string(), bcolors.Green, name, 'is_on True', bcolors.End)
#     await light(name).is_on.write(True)
#
#
# async def turn_off(name):
#     # print(get_time_string(), bcolors.Red, name, 'is_on False', bcolors.End)
#     await light(name).is_on.write(False)
#
#
# async def recup_on():
#     # print(get_time_string(), bcolors.Green, name, 'is_on True', bcolors.End)
#     await fan('prizemi.garaz.rekuperace').is_on.write(True)
#
#
# async def recup_off():
#     # print(get_time_string(), bcolors.Red, name, 'is_on False', bcolors.End)
#     await fan('prizemi.garaz.rekuperace').is_on.write(False)


async def blinds_calibrate():
    for name in blinds_all_names:
        await blinds(name).characteristics['calibrate'].write(None)
        await asyncio.sleep(blinds_timeout)


async def blinds_blind_mode(mode):
    for name in blinds_all_names:
        await blindsControl(name).characteristics['mode'].write(mode)
        await asyncio.sleep(blinds_timeout)


async def blinds_blind_all(blind):
    for name in blinds_all_names:
        await blinds(name).target_blind.write(blind)
        await asyncio.sleep(blinds_timeout)


async def blinds_tilt_all(tilt):
    for name in blinds_all_names:
        await blinds(name).target_tilt.write(tilt)
        await asyncio.sleep(blinds_timeout)


async def blinds_obyvak_blind_mode(mode):
    for name in blinds_obyvak_names:
        await blindsControl(name).characteristics['mode'].write(mode)
        await asyncio.sleep(blinds_timeout)


async def blinds_obyvak_blind_all(blind):
    for name in blinds_obyvak_names:
        await blinds(name).target_blind.write(blind)
        await asyncio.sleep(blinds_timeout)


async def blinds_obyvak_tilt_all(tilt):
    for name in blinds_obyvak_names:
        await blinds(name).target_tilt.write(tilt)
        await asyncio.sleep(blinds_timeout)
