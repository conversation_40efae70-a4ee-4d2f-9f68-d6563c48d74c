import asyncio
import time
import logging

import arrow
from caspia.pan import get_global_pan
from caspia.pan import reactive as r
from pan.integrations.notificator import TelegramNotificator

pan = get_global_pan()
logger = logging.getLogger(__name__)

automator = pan.automator
telegram = TelegramNotificator('-330771411')
door = pan.lookup.door
outlet = pan.lookup.outlet

rezim_leto = pan.builder.create_switch('switch.rezim.leto')


# Vlastní pomocné funkce pro kontrolu měsíce a dne
def month_is(m):
    """Vytvoří observable, který je True pro daný měsíc."""
    return r.lambda_(lambda _: arrow.now().month == m, r.every_hour)

def day_is(d):
    """Vytvoří observable, který je True pro daný den v měsíci."""
    return r.lambda_(lambda _: arrow.now().day == d, r.every_hour)


# automatické zapnutí letního režimu 1. května v daném roce
@pan.automator.create_automation('scene: automatické zapnutí letního režimu 1. května v daném roce')
@pan.automator.when(r.time_is('1:00') & month_is(5) & day_is(1))
async def zapni_rezim_leto():
    await rezim_leto.is_on.write(True)


# automatické vypnutí letního režimu 1. září v daném roce
@pan.automator.create_automation('scene: automatické vypnutí letního režimu 1. září v daném roce')
@pan.automator.when(r.time_is('1:00') & month_is(9) & day_is(1))
async def vypni_rezim_leto():
    await rezim_leto.is_on.write(False)


