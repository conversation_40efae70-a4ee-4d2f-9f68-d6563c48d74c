import asyncio

from caspia.pan import get_global_pan
from caspia.pan import reactive as r
from pan.integrations.notificator import TelegramNotificator
import os

pan = get_global_pan()
door = pan.lookup.door
button = pan.lookup.button
telegram = TelegramNotificator(os.environ.get('CSP_TELEGRAM_GROUP_ID'))
automator = pan.automator

nekdo_je_doma = r.someone_present()
nikdo_neni_doma = r.everyone_away()
nekde_detekovan_pohyb = button('prizemi.kuchyn.detekce_pohybu').is_pushed | \
                        button('prizemi.zadveri.detekce_pohybu').is_pushed | \
                        button('prizemi.chodba.detekce_pohybu_vstup').is_pushed | \
                        button('prizemi.chodba.detekce_pohybu').is_pushed
nikdo_neni_doma_a_detekovan_pohyb = nikdo_neni_doma & nekde_detekovan_pohyb
poslat_notifikaci_o_pohybu = r.Value(True)
detekovano_rozbiti_skla = button('prizemi.kuchyn.detekce_rozbiti').is_pushed
nekde_detekova_pozar = button('prizemi.zadveri.pozarni_hlasic').is_pushed | \
                        button('prizemi.chodba.pozarni_hlasic').is_pushed
zaplavena_koupelna = door('prizemi.kouplna.detekce_zaplaveni').is_open
zaplavena_pradelna = door('prizemi.pradelna.detekce_zaplaveni').is_open


@automator.create_automation('Security: Detekován pohyb, když nikdo není doma')
@automator.when(nikdo_neni_doma_a_detekovan_pohyb & poslat_notifikaci_o_pohybu)
async def notifikace_detekovan_pohyb():
    await telegram.raise_notification('‼️️ Detekován pohyb v domě ‼️')
    await poslat_notifikaci_o_pohybu.update(False)
    await asyncio.sleep(60)
    await poslat_notifikaci_o_pohybu.update(True)


@automator.create_automation('Security: Detekováno rozbití skla v kuchyni')
@automator.when(detekovano_rozbiti_skla)
async def notifikace_rozbite_sklo():
    await telegram.raise_notification('‼️️ Detekováno rozbití skla v kuchyni 💥')


@automator.create_automation('Security: Detekováno rozbití skla v kuchyni')
@automator.when(nekde_detekova_pozar)
async def notifikace_pozar():
    await telegram.raise_notification('‼️ Detekován požár 🔥')


@automator.create_automation('Security: Detekováno zaplavení koupelny')
@automator.when(zaplavena_koupelna)
async def notifikace_zaplavena_koupelna():
    await telegram.raise_notification('‼️ Detekováno zaplavení koupelny 💦')


@automator.create_automation('Security: Detekováno zaplavení prádelny')
@automator.when(zaplavena_pradelna)
async def notifikace_zaplavena_pradelna():
    await telegram.raise_notification('‼️ Detekováno zaplavení prádelny 💦')
