import asyncio
from caspia.pan import get_global_pan

pan = get_global_pan()
pritomnost = pan.lookup.presence('dum.pritomnost')
light = pan.lookup.light
switch = pan.lookup.switch
automator = pan.automator
pritomnost_host = pan.builder.create_switch('switch.pritomnost.host').is_on


import logging
logger = logging.getLogger(__name__)


@automator.create_automation('Presence: host')
@automator.on_update(pritomnost_host)
async def pritomnost_host_set(value):
    logger.info('presence: pritomnost_host updated')
    if value:
        logger.info('presence: pritomnost_host present')
        state_update = {'host': {'timeout': None, 'state': 'present', 'source': 'manual'}}
        await pritomnost.update.write(state_update)
    else:
        logger.info('presence: pritomnost_host away')
        state_update = {'host': {'timeout': None, 'state': 'away', 'source': 'manual'}}
        await pritomnost.update.write(state_update)


async def host_on():
    await light('prizemi.zadveri.hlavni1').toggle()
    await asyncio.sleep(1.0)
    await light('prizemi.zadveri.hlavni1').toggle()

    await pritomnost_host.write(True)


async def host_off():
    await light('prizemi.zadveri.hlavni1').toggle()
    await asyncio.sleep(0.2)
    await light('prizemi.zadveri.hlavni1').toggle()
    await asyncio.sleep(0.2)
    await light('prizemi.zadveri.hlavni1').toggle()
    await asyncio.sleep(0.2)
    await light('prizemi.zadveri.hlavni1').toggle()

    await pritomnost_host.write(False)
