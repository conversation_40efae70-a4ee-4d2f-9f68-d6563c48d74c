from caspia.pan import get_global_pan

from ..utils import (BlindsMode, blinds_blind_all, blinds_blind_mode, blinds_calibrate, blinds_tilt_all)

pan = get_global_pan()
namespace = pan.lookup.namespace
button = pan.lookup.button
blinds = pan.lookup.blinds

@pan.builder.create_switch_with_action('switch.zaluzie.vse.zavrit')
async def blinds_close_all():
    await blinds_blind_all(1.0)
    await blinds_tilt_all(90.0)

@pan.builder.create_switch_with_action('switch.zaluzie.vse.otevrit')
async def blinds_open_all():
    await blinds_blind_all(0.0)
    await blinds_tilt_all(0.0)

@pan.builder.create_switch_with_action('switch.zaluzie.vse.horizontalne')
async def blinds_horizontal_all():
    await blinds_blind_all(1.0)
    await blinds_tilt_all(-10.0)

@pan.builder.create_switch_with_action('switch.zaluzie.vse.block')
async def blinds_block_all():
    await blinds_blind_mode(BlindsMode.block)


@pan.builder.create_switch_with_action('switch.zaluzie.vse.manual')
async def blinds_manual_all():
    await blinds_blind_mode(BlindsMode.manual)


@pan.builder.create_switch_with_action('switch.zaluzie.vse.calibrate')
async def blinds_calibrate_all():
    await blinds_calibrate()
