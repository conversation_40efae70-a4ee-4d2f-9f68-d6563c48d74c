from caspia.pan import get_global_pan
from caspia.pan.rules import BlindsControlRule

pan = get_global_pan()
button = pan.lookup.button
blinds = pan.lookup.blinds

pan.rules_manager.add(BlindsControlRule(
    identifier='patro.detsky_pokoj.stred',
    horizontal_view=(240, 30),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('patro.detsky_pokoj.stred')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='patro.detsky_pokoj.vlevo',
    horizontal_view=(156, 230),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('patro.detsky_pokoj.vlevo')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='patro.detsky_pokoj.vpravo',
    horizontal_view=(230, 30),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('patro.detsky_pokoj.vpravo')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='patro.koupelna.hlavni',
    horizontal_view=(80, 207),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('patro.koupelna.hlavni')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='patro.loznice.hlavni',
    horizontal_view=(148, 320),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('patro.loznice.hlavni')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='patro.pracovna.hlavni',
    horizontal_view=(46, 230),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('patro.pracovna.hlavni')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='prizemi.koupelna.hlavni',
    horizontal_view=(70, 216),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('prizemi.koupelna.hlavni')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='prizemi.kuchyn.hlavni',
    horizontal_view=(72, 217),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('prizemi.kuchyn.hlavni')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='prizemi.loznice.hlavni',
    horizontal_view=(236, 30),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('prizemi.loznice.hlavni')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='prizemi.obyvak.fix',
    horizontal_view=(169, 314),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('prizemi.obyvak.fix')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='prizemi.obyvak.terasa_fix',
    horizontal_view=(154, 260),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('prizemi.obyvak.terasa_fix')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='prizemi.obyvak.terasa_vstup',
    horizontal_view=(154, 260),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('prizemi.obyvak.terasa_vstup')
))

pan.rules_manager.add(BlindsControlRule(
    identifier='prizemi.obyvak.vpravo',
    horizontal_view=(253, 10),
    vertical_view=(3, 66),
    blinds_seg_width=9.0,
    blinds_seg_spacing=8.0,
    blinds=pan.lookup.blinds('prizemi.obyvak.vpravo')
))
