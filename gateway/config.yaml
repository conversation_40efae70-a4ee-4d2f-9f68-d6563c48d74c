allowed-canids:
    # the CAN identifiers that can be assigned by this gateway to its nodes
    '*': '0x00-0x1EF'
lightgroup-identifiers-range:
    # the lightgroup identifier range that can be assigned by this gateway to its nodes
    '0x0-0x1f'
nodes:
    prizemi.technicka.rmb_351: 440403030000535317d5545f070000f5        # node 971
    prizemi.technicka.rmb_352: 431303030000535394cf545f070000f5        # node 972
    prizemi.technicka.rmb_353: 41140303000053530fcb545f010000f5        # node 973
    prizemi.technicka.rmb_354: 43fe0303000053530dd5545f020000f5        # node 974
    prizemi.technicka.rmb_355: 430403030000535332d0545f000000f5        # node 975
    prizemi.technicka.rmb_356: 4417030300005353f3ca545f020000f5        # node 976
    prizemi.technicka.rmb_357: 43000303000053530dd5545f040000f5        # node 977
    prizemi.technicka.rmb_358: 42110303000053538bcf545f050000f5        # node 978
    prizemi.technicka.rmb_359: 41150303000053530fcb545f020000f5        # node 979 žaluzie
    prizemi.technicka.rmb_360: 100f030300005353b9cd545f050000f5        # node 980 žaluzie
    prizemi.technicka.rmb_361: 420303030000535304d5545f070000f5        # node 981 žaluzie
    prizemi.technicka.rmb_362: 1015030300005353e2cc545f030000f5        # node 982 žaluzie
    prizemi.technicka.rmb_363: 0f13030300005353ebcc545f000000f5        # node 983 žaluzie
    prizemi.technicka.rmb_364: 440303030000535317d5545f060000f5        # node 984 žaluzie
    prizemi.technicka.rmb_365: 0f0e030300005353afcd545f030000f5        # node 985 žaluzie
    prizemi.technicka.rmb_366: 4314030300005353fcca545f000000f5        # node 986
    prizemi.technicka.rmb_367: 3f1903030000535322cb545f070000f5        # node 987
    prizemi.technicka.rmb_368: 42f4030300005353e5d5545f000000f5        # node 988
    prizemi.technicka.rmb_369: 42060303000053533cd0545f020000f5        # node 989
    prizemi.technicka.rmb_370: 4511030300005353a7cf545f030000f5        # node 990
    prizemi.technicka.rmb_371: 43ef0303000053536edb545f030000f5        # node 991
    prizemi.technicka.rmb_372: 42ee03030000535365db545f020000f5        # node 992
    prizemi.technicka.rmb_373: 42f7030300005353e5d5545f030000f5        # node 993
    prizemi.technicka.rmb_374: 0f0a03030000535318d2545f070000f5        # node 994
    prizemi.technicka.rmb_394: 110603030000535305d2545f050000f5        # node 1043

    prizemi.zadveri.u_vstupu: 100c030300005353b9cd545f020000f5         # node 1044, SB 601
    prizemi.zadveri.u_chodby: 110d030300005353c2cd545f040000f5         # node 1045, SB 602
    prizemi.technicka.u_vstupu: 11f903030000535330d3545f000000f5         # node 1046, SB 603
    prizemi.chodba.u_vstupu: 12f1030300005353bbd8545f000000f5         # node 1047, SB 604
    prizemi.chodba.u_loznice: 12ec030300005353a5d9545f030000f5         # node 1048, SB 605
    prizemi.chodba.u_schodu: 11f8030300005353c4d8545f070000f5         # node 1073, SB 630
    prizemi.loznice.u_vstupu: 13eb030300005353aed9545f030000f5         # node 1049, SB 606
    prizemi.loznice.postel_vlevo: 110003030000535330d3545f070000f5         # node 1050, SB 607
    prizemi.loznice.postel_vpravo: 420d0303000053538bcf545f010000f5         # node 1051, SB 608
    prizemi.obyvak.u_vstupu: 11ff03030000535330d3545f060000f5         # node 1052, SB 609
    prizemi.obyvak.u_okna1: 110403030000535305d2545f030000f5         # node 1053, SB 610
    prizemi.obyvak.u_okna2: 13f4030300005353b2d8545f040000f5         # node 1054, SB 611
    prizemi.kuchyn.u_terasy1: 11f5030300005353c4d8545f040000f5         # node 1055, SB 612
    prizemi.kuchyn.u_terasy2: 12f6030300005353bbd8545f050000f5         # node 1074, SB 631
    prizemi.kuchyn.drez_vpravo: 11f3030300005353c4d8545f020000f5         # node 1057, SB 614
    prizemi.kuchyn.drez_vlevo: 11fd03030000535330d3545f040000f5         # node 1058, SB 615
    prizemi.koupelna.u_vstupu: 11f4030300005353c4d8545f030000f5         # node 1059, SB 616
    prizemi.sauna.u_vstupu: 11f7030300005353c4d8545f060000f5         # node 1060, SB 617

    patro.chodba.u_vstupu: 13ee030300005353aed9545f060000f5         # node 1061, SB 618
    patro.pracovna.u_vstupu: 12ea030300005353a5d9545f010000f5         # node 1062, SB 619
    patro.chodba.u_pokoje: 110a030300005353c2cd545f010000f5         # node 1063, SB 620
    patro.chodba.u_koupelny: 110b030300005353c2cd545f020000f5         # node 1064, SB 621
    patro.koupelna.u_vstupu: 110503030000535305d2545f040000f5         # node 1065, SB 622
    patro.loznice.u_vstupu: 110703030000535305d2545f060000f5         # node 1066, SB 623
    patro.loznice.postel_vlevo: 11fe03030000535330d3545f050000f5         # node 1067, SB 624
    patro.loznice.postel_vpravo: 12f7030300005353bbd8545f060000f5         # node 1068, SB 625
    patro.detsky_pokoj.u_vstupu: 110203030000535305d2545f010000f5         # node 1069, SB 626
    patro.detsky_pokoj.za_rohem: 13ef030300005353aed9545f070000f5         # node 1070, SB 627
    patro.detsky_pokoj.u_okna: 110103030000535305d2545f000000f5         # node 1071, SB 628
    patro.puda.u_vstupu: 13f5030300005353b2d8545f050000f5         # node 1072, SB 629

#    okoli.mereni.venku: 11f8030300005353c4d8545f070000f5         # node 1073, SB 630

services:
    prizemi:
        koupelna:
            - { type: blinds, name: hlavni, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_362, power_pin: 1, direction_pin: 0, timing: !include zaluzie_prizemi_koupelna.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: light, name: hlavni.tmp, relay: { node: prizemi.technicka.rmb_354, pin: 1, initial_state: true } }
            # ??? v plánech technická místnost (asi to bude WC, ale blbne)
            - { type: light, name: wc, relay: { node: prizemi.technicka.rmb_354, pin: 2 } }
            # pravděpodobně nelze napojit světlo na žaluziový FW - spíš problém s verzí pythonu
            - { type: light, name: nika, relay: { node: prizemi.technicka.rmb_365, pin: 1 } }

            - { type: outlet, name: topny_zebrik, relay: { node: prizemi.technicka.rmb_366, pin: 3, timeout: 1500 } }
            - { type: outlet, name: topeni, relay: { node: prizemi.technicka.rmb_369, pin: 0 } }    # KA19 K4  hlavice7  koupelna

            - { type: button, name: u_vstupu.nahoru, location: { node: prizemi.koupelna.u_vstupu, pin: 1 } }
            - { type: button, name: u_vstupu.dolu, location: { node: prizemi.koupelna.u_vstupu, pin: 2 } }
            - { type: button, name: u_vstupu.vlevo, location: { node: prizemi.koupelna.u_vstupu, pin: 7 } } # hlavní světlo, dlouhý stisk LED nika
            - { type: button, name: u_vstupu.vpravo, location: { node: prizemi.koupelna.u_vstupu, pin: 3 } }

            - { type: temperature-sensor, name: u_vstupu, mcp980x: { node: prizemi.koupelna.u_vstupu, interval: 30 } }

        loznice:
            - { type: blinds, name: hlavni, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_362, power_pin: 2, direction_pin: 3, timing: !include zaluzie_prizemi_loznice.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: light, name: hlavni, relay: { node: prizemi.technicka.rmb_355, pin: 3 } }
            # ??? kuchyn 1.7.12
            - { type: light, name: K2, relay: { node: prizemi.technicka.rmb_355, pin: 2 } }

            - { type: outlet, name: topeni, relay: { node: prizemi.technicka.rmb_368, pin: 1 } }    # KA18 K3  hlavice2  pokoj

            - { type: button, name: u_vstupu.nahoru, location: { node: prizemi.loznice.u_vstupu, pin: 2 } }
            - { type: button, name: u_vstupu.dolu, location: { node: prizemi.loznice.u_vstupu, pin: 3 } }
            - { type: button, name: u_vstupu.vpravo, location: { node: prizemi.loznice.u_vstupu, pin: 1 } } # hlavní

            - { type: button, name: postel_vlevo.nahoru, location: { node: prizemi.loznice.postel_vlevo, pin: 2 } }
            - { type: button, name: postel_vlevo.dolu, location: { node: prizemi.loznice.postel_vlevo, pin: 3 } }
            - { type: button, name: postel_vlevo.vpravo, location: { node: prizemi.loznice.postel_vlevo, pin: 1 } } # hlavní

            - { type: button, name: postel_vpravo.nahoru, location: { node: prizemi.loznice.postel_vpravo, pin: 2 } }
            - { type: button, name: postel_vpravo.dolu, location: { node: prizemi.loznice.postel_vpravo, pin: 3 } }
            - { type: button, name: postel_vpravo.vlevo, location: { node: prizemi.loznice.postel_vpravo, pin: 1 } } # hlavní

            - { type: carbon-dioxide-sensor, name: u_vstupu, scd30: &scd_loznice_prizemi {
                    node: prizemi.loznice.u_vstupu, interval: 60, temperature_offset: 3.1, ambient_pressure: 1020, altitude_compensation: 389 } }
            - { type: temperature-sensor, name: u_vstupu, scd30: *scd_loznice_prizemi }
            - { type: humidity-sensor, name: u_vstupu, scd30: *scd_loznice_prizemi }

        kuchyn:
            - { type: blinds, name: hlavni, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_365, power_pin: 2, direction_pin: 3, timing: !include zaluzie_prizemi_kuchyn.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: light, name: hlavni.tmp, relay: { node: prizemi.technicka.rmb_355, pin: 0, initial_state: true } }
            - { type: light, name: nad_kuchynskou_linkou, relay: { node: prizemi.technicka.rmb_356, pin: 3 } }
            - { type: outlet, name: digestor, relay: { node: prizemi.technicka.rmb_355, pin: 1, initial_state: true } }
            - { type: outlet, name: ostruvek, relay: { node: prizemi.technicka.rmb_367, pin: 3, initial_state: true } }     # jistič FA33
            - { type: outlet, name: linka, relay: { node: prizemi.technicka.rmb_367, pin: 2, initial_state: true } }           # jistič FA41 (asi)
            - { type: outlet, name: nad_hornima_skrinkama, relay: { node: prizemi.technicka.rmb_367, pin: 1, initial_state: true } }

            - { type: outlet, name: topeni, relay: { node: prizemi.technicka.rmb_369, pin: 3 } }    # KA19 K1  hlavice6  kuchyň

            # levá je vstup přes dveře
            - { type: button, name: u_terasy1.vlevo_dolu, location: { node: prizemi.kuchyn.u_terasy1, pin: 3 } }
            - { type: button, name: u_terasy1.vlevo_nahoru, location: { node: prizemi.kuchyn.u_terasy1, pin: 2 } }
            - { type: button, name: u_terasy1.vpravo_dolu, location: { node: prizemi.kuchyn.u_terasy1, pin: 1 } }
            - { type: button, name: u_terasy1.vpravo_nahoru, location: { node: prizemi.kuchyn.u_terasy1, pin: 0 } }

            # todo: problémový prvek
            - { type: button, name: u_terasy2.3, location: { node: prizemi.kuchyn.u_terasy2, pin: 3 } }
            - { type: button, name: u_terasy2.2, location: { node: prizemi.kuchyn.u_terasy2, pin: 2 } }
            - { type: button, name: u_terasy2.1, location: { node: prizemi.kuchyn.u_terasy2, pin: 1 } }
            - { type: button, name: u_terasy2.0, location: { node: prizemi.kuchyn.u_terasy2, pin: 0 } }

            - { type: button, name: drez_vpravo.dolu, location: { node: prizemi.kuchyn.drez_vpravo, pin: 3 } }
            - { type: button, name: drez_vpravo.nahoru, location: { node: prizemi.kuchyn.drez_vpravo, pin: 2 } }
            - { type: button, name: drez_vpravo.pravy, location: { node: prizemi.kuchyn.drez_vpravo, pin: 1 } } # hlavni

            - { type: button, name: drez_vlevo.levy, location: { node: prizemi.kuchyn.drez_vlevo, pin: 3 } }    # hlavni

            - { type: temperature-sensor, name: drez, mcp980x: { node: prizemi.kuchyn.drez_vpravo, interval: 30 } }

        obyvak:
            - { type: blinds, name: vpravo, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_363, power_pin: 2, direction_pin: 3, timing: !include zaluzie_prizemi_obyvak_vpravo.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: blinds, name: fix, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_363, power_pin: 1, direction_pin: 0, timing: !include zaluzie_prizemi_obyvak_fix.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: blinds, name: terasa_vstup, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_364, power_pin: 2, direction_pin: 3, timing: !include zaluzie_prizemi_obyvak_terasa.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: blinds, name: terasa_fix, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_364, power_pin: 1, direction_pin: 0, timing: !include zaluzie_prizemi_obyvak_terasa.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            # ??? obyvak 1.7.15
            #- { type: light, name: K1, relay: { node: prizemi.technicka.rmb_356, pin: 3 } }
            # ??? sauna, ale funguje
            - { type: light, name: K2, relay: { node: prizemi.technicka.rmb_356, pin: 2 } }
            # ??? venkovni vstup do domu WS 0.1
            - { type: light, name: K3, relay: { node: prizemi.technicka.rmb_356, pin: 1 } }

            - { type: outlet, name: tv, relay: { node: prizemi.technicka.rmb_367, pin: 0, initial_state: true } }

            - { type: outlet, name: topeni3, relay: { node: prizemi.technicka.rmb_369, pin: 2 } }    # KA19 K2  hlavice5  obývák 3
            - { type: outlet, name: topeni2, relay: { node: prizemi.technicka.rmb_368, pin: 3 } }    # KA18 K1  hlavice4  obývák 2
            - { type: outlet, name: topeni1, relay: { node: prizemi.technicka.rmb_368, pin: 2 } }    # KA18 K2  hlavice3  obývák 1

            - { type: button, name: u_vstupu.levy, location: { node: prizemi.obyvak.u_vstupu, pin: 3 } } # chodba
            - { type: button, name: u_vstupu.dolu, location: { node: prizemi.obyvak.u_vstupu, pin: 2 } } # žaluzie všechny najednou i kuchyň
            - { type: button, name: u_vstupu.nahoru, location: { node: prizemi.obyvak.u_vstupu, pin: 1 } }
            - { type: button, name: u_vstupu.1, location: { node: prizemi.obyvak.u_vstupu, pin: 0 } } # linka, dlouhý dřevník LED
            - { type: button, name: u_vstupu.2, location: { node: prizemi.obyvak.u_vstupu, pin: 8 } } # kuchyn.hlavni, dlouhý kuchyn nad skříňkou
            - { type: button, name: u_vstupu.3, location: { node: prizemi.obyvak.u_vstupu, pin: 9 } } # světlo chybí (stůl, zatím není)
            - { type: button, name: u_vstupu.4, location: { node: prizemi.obyvak.u_vstupu, pin: 7 } } # obývák 1.7.15

            - { type: button, name: u_okna1.vlevo_dolu, location: { node: prizemi.obyvak.u_okna1, pin: 3 } }
            - { type: button, name: u_okna1.vlevo_nahoru, location: { node: prizemi.obyvak.u_okna1, pin: 2 } }
            - { type: button, name: u_okna1.vpravo_dolu, location: { node: prizemi.obyvak.u_okna1, pin: 1 } }
            - { type: button, name: u_okna1.vpravo_nahoru, location: { node: prizemi.obyvak.u_okna1, pin: 0 } }

            - { type: button, name: u_okna2.vlevo, location: { node: prizemi.obyvak.u_okna2, pin: 3 } }
            - { type: button, name: u_okna2.vpravo, location: { node: prizemi.obyvak.u_okna2, pin: 2 } }

            - { type: carbon-dioxide-sensor, name: u_okna2, scd30: &scd_obyvak {
                    node: prizemi.obyvak.u_okna2, interval: 60, temperature_offset: 3.1, ambient_pressure: 1020, altitude_compensation: 389 } }
            - { type: temperature-sensor, name: u_okna2, scd30: *scd_obyvak }
            - { type: humidity-sensor, name: u_okna2, scd30: *scd_obyvak }

        technicka:
            - { type: light, name: sklep, relay: { node: prizemi.technicka.rmb_353, pin: 0 } }
            - { type: light, name: hlavni, relay: { node: prizemi.technicka.rmb_354, pin: 0 } }
            - { type: outlet, name: zdroj_led1, relay: { node: prizemi.technicka.rmb_358, pin: 3, initial_state: true } }
            - { type: outlet, name: zdroj_led2, relay: { node: prizemi.technicka.rmb_358, pin: 1, initial_state: true } }

            # KA20
            - { type: outlet, name: topna_hlavice9, relay: { node: prizemi.technicka.rmb_370, pin: 3 } }    # KA20 K1  hlavice9  1 patro ???

            - { type: button, name: u_vstupu.levy, location: { node: prizemi.technicka.u_vstupu, pin: 3 } } # sklep
            - { type: button, name: u_vstupu.pravy, location: { node: prizemi.technicka.u_vstupu, pin: 2 } } # hlavní

        zadveri:
            - { type: light, name: hlavni.tmp, relay: { node: prizemi.technicka.rmb_353, pin: 3, initial_state: true } }
            - { type: button, name: u_vstupu.levy, location: { node: prizemi.zadveri.u_vstupu, pin: 3 } } # vchod (bude)
            - { type: button, name: u_vstupu.pravy, location: { node: prizemi.zadveri.u_vstupu, pin: 2 } } # krátké hlavní, dlouhé odchodové
            - { type: button, name: u_chodby.levy, location: { node: prizemi.zadveri.u_chodby, pin: 3 } } # hlavni
            - { type: button, name: u_chodby.pravy, location: { node: prizemi.zadveri.u_chodby, pin: 2 } } # chodba.hlavni
            - { type: outlet, name: dvere, relay: { node: prizemi.technicka.rmb_394, pin: 3, timeout: 1 } }

        chodba:
            - { type: light, name: schody, relay: { node: prizemi.technicka.rmb_353, pin: 2 } }
            - { type: light, name: hlavni.tmp, relay: { node: prizemi.technicka.rmb_353, pin: 1, initial_state: true } }
            - { type: light, name: led, relay: { node: prizemi.technicka.rmb_354, pin: 3 } }

            - { type: outlet, name: topeni, relay: { node: prizemi.technicka.rmb_368, pin: 0 } }    # KA18 K4  hlavice1  chodba

            - { type: button, name: u_vstupu.levy, location: { node: prizemi.chodba.u_vstupu, pin: 3 } } # zadveri
            - { type: button, name: u_vstupu.pravy, location: { node: prizemi.chodba.u_vstupu, pin: 2 } } # hlavni
            - { type: button, name: u_schodu.levy, location: { node: prizemi.chodba.u_schodu, pin: 2 } } # hlavni
            - { type: button, name: u_loznice.levy, location: { node: prizemi.chodba.u_loznice, pin: 3 } } # hlavni
            - { type: button, name: detekce_pohybu, location: { node: prizemi.technicka.rmb_351, pin: 7 } }    # PIR 1.1.1
            - { type: button, name: detekce_eps, location: { node: prizemi.technicka.rmb_351, pin: 6 } }    # dolní EPS
            # PIR rozsvítí jen LED na minutu

        sauna:
            - { type: light, name: hlavni, relay: { node: prizemi.technicka.rmb_356, pin: 0 } }

            - { type: outlet, name: topeni, relay: { node: prizemi.technicka.rmb_369, pin: 1 } }    # KA19 K3  hlavice8  sauna

            - { type: button, name: u_vstupu.levy, location: { node: prizemi.sauna.u_vstupu, pin: 3 } } # hlavni (bude)
            - { type: button, name: u_vstupu.pravy, location: { node: prizemi.sauna.u_vstupu, pin: 2 } } # krátký rozsvítí LED (budou), dlouhý stisk vypnout osvětlení v koupelně

        venku:
            - { type: outlet, name: terasa, relay: { node: prizemi.technicka.rmb_366, pin: 1 } }    # terasa
            - { type: outlet, name: u_vsatupu, relay: { node: prizemi.technicka.rmb_366, pin: 0 } }    # u vstupu obě
            - { type: outlet, name: retencni_nadrz, relay: { node: prizemi.technicka.rmb_372, pin: 1, timeout: 1800 } }
            - { type: button, name: detekce_pohybu, location: { node: prizemi.technicka.rmb_353, pin: 7 } }    # PIN venku, ale ještě není

    patro:
        loznice:
            # ??? blbnoucí žaluzie
            - { type: blinds, name: hlavni, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_361, power_pin: 2, direction_pin: 3, timing: !include zaluzie_patro_loznice.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: light, name: hlavni.tmp, relay: { node: prizemi.technicka.rmb_352, pin: 3, initial_state: true } }
            - { type: button, name: u_vstupu.levy, location: { node: patro.loznice.u_vstupu, pin: 3 } }
            - { type: button, name: u_vstupu.nahoru, location: { node: patro.loznice.u_vstupu, pin: 1 } }
            - { type: button, name: u_vstupu.dolu, location: { node: patro.loznice.u_vstupu, pin: 2 } }
            - { type: button, name: postel_vlevo.pravy, location: { node: patro.loznice.postel_vlevo, pin: 1 } } # hlavni
            - { type: button, name: postel_vlevo.nahoru, location: { node: patro.loznice.postel_vlevo, pin: 2 } }
            - { type: button, name: postel_vlevo.dolu, location: { node: patro.loznice.postel_vlevo, pin: 3 } }
            - { type: button, name: postel_vpravo.levy, location: { node: patro.loznice.postel_vpravo, pin: 1 } } # hlavni
            - { type: button, name: postel_vpravo.nahoru, location: { node: patro.loznice.postel_vpravo, pin: 2 } }
            - { type: button, name: postel_vpravo.dolu, location: { node: patro.loznice.postel_vpravo, pin: 3 } }

            - { type: carbon-dioxide-sensor, name: u_vstupu, scd30: &scd_loznice {
                    node: patro.loznice.u_vstupu, interval: 60, temperature_offset: 3.1, ambient_pressure: 1020, altitude_compensation: 389 } }
            - { type: temperature-sensor, name: u_vstupu, scd30: *scd_loznice }
            - { type: humidity-sensor, name: u_vstupu, scd30: *scd_loznice }

        detsky_pokoj:
            - { type: blinds, name: vpravo, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_359, power_pin: 1, direction_pin: 0, timing: !include zaluzie_patro_detsky_pokoj.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: blinds, name: stred, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_360, power_pin: 1, direction_pin: 0, timing: !include zaluzie_patro_detsky_pokoj.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: blinds, name: vlevo, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_360, power_pin: 2, direction_pin: 3, timing: !include zaluzie_patro_detsky_pokoj.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: light, name: u_okna, relay: { node: prizemi.technicka.rmb_351, pin: 2 } }
            - { type: light, name: hlavni, relay: { node: prizemi.technicka.rmb_351, pin: 1 } }
            - { type: button, name: u_vstupu.pravy, location: { node: patro.detsky_pokoj.u_vstupu, pin: 0 } } # levé světlo
            - { type: button, name: u_vstupu.levy, location: { node: patro.detsky_pokoj.u_vstupu, pin: 1 } }  # pravé světlo
            - { type: button, name: u_vstupu.nahoru, location: { node: patro.detsky_pokoj.u_vstupu, pin: 2 } }
            - { type: button, name: u_vstupu.dolu, location: { node: patro.detsky_pokoj.u_vstupu, pin: 3 } } # všechny žaluzie
            - { type: button, name: za_rohem.pravy, location: { node: patro.detsky_pokoj.za_rohem, pin: 1 } } # jen to levé světlo
            - { type: button, name: za_rohem.nahoru, location: { node: patro.detsky_pokoj.za_rohem, pin: 2 } } # všechny žaluzie
            - { type: button, name: za_rohem.dolu, location: { node: patro.detsky_pokoj.za_rohem, pin: 3 } }
            - { type: button, name: u_okna.nahoru, location: { node: patro.detsky_pokoj.u_okna, pin: 2 } } # jen tu jednu žaluzii
            - { type: button, name: u_okna.dolu, location: { node: patro.detsky_pokoj.u_okna, pin: 3 } }

            - { type: temperature-sensor, name: za_rohem, mcp980x: { node: patro.detsky_pokoj.za_rohem, interval: 30 } }

        pracovna:
            - { type: blinds, name: hlavni, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_359, power_pin: 2, direction_pin: 3, timing: !include zaluzie_patro_pracovna.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: light, name: hlavni, relay: { node: prizemi.technicka.rmb_351, pin: 0 } }
            - { type: button, name: u_vstupu.pravy, location: { node: patro.pracovna.u_vstupu, pin: 1 } } # hlavní
            - { type: button, name: u_vstupu.nahoru, location: { node: patro.pracovna.u_vstupu, pin: 2 } }
            - { type: button, name: u_vstupu.dolu, location: { node: patro.pracovna.u_vstupu, pin: 3 } }
            # KA20
            - { type: outlet, name: topna_hlavice3, relay: { node: prizemi.technicka.rmb_370, pin: 2 } }    # K2
            - { type: outlet, name: topna_hlavice2, relay: { node: prizemi.technicka.rmb_370, pin: 1 } }    # K3
            - { type: outlet, name: topna_hlavice1, relay: { node: prizemi.technicka.rmb_370, pin: 0 } }    # K4
            # KA21
            - { type: outlet, name: topna_hlavice6, relay: { node: prizemi.technicka.rmb_371, pin: 3 } }    # K1
            - { type: outlet, name: topna_hlavice5, relay: { node: prizemi.technicka.rmb_371, pin: 2 } }    # K2
            #- { type: outlet, name: topna_hlaviceX, relay: { node: prizemi.technicka.rmb_371, pin: 1 } }    # K3 není zapojené
            - { type: outlet, name: topna_hlavice4, relay: { node: prizemi.technicka.rmb_371, pin: 0 } }    # K4

            - { type: carbon-dioxide-sensor, name: u_vstupu, scd30: &scd_pracovna {
                    node: patro.pracovna.u_vstupu, interval: 60, temperature_offset: 3.1, ambient_pressure: 1020, altitude_compensation: 389 } }
            - { type: temperature-sensor, name: u_vstupu, scd30: *scd_pracovna }
            - { type: humidity-sensor, name: u_vstupu, scd30: *scd_pracovna }

        koupelna:
            - { type: blinds, name: hlavni, angle_mapping: { 0.2: 18, 0.6: 54 }, component: { node: prizemi.technicka.rmb_361, power_pin: 1, direction_pin: 0, timing: !include zaluzie_patro_koupelna.yaml, calibrate_at_bottom: true, direction_inverted: true } }
            - { type: light, name: hlavni.tmp, relay: { node: prizemi.technicka.rmb_352, pin: 0, initial_state: true } }
            - { type: light, name: wc, relay: { node: prizemi.technicka.rmb_352, pin: 1 } } # TODO mrknout příště
            # pravděpodobně nelze napojit světlo na žaluziový FW - spíš problém s verzí pythonu
            - { type: light, name: nika, relay: { node: prizemi.technicka.rmb_365, pin: 0 } }
            - { type: button, name: u_vstupu.vlevo, location: { node: patro.koupelna.u_vstupu, pin: 7 } } # rozsvítit všechna světla, včetně WC
            - { type: button, name: u_vstupu.nahoru, location: { node: patro.koupelna.u_vstupu, pin: 1 } }
            - { type: button, name: u_vstupu.dolu, location: { node: patro.koupelna.u_vstupu, pin: 2 } }
            - { type: button, name: u_vstupu.vpravo, location: { node: patro.koupelna.u_vstupu, pin: 3 } }

            - { type: outlet, name: topny_zebrik, relay: { node: prizemi.technicka.rmb_372, pin: 3, timeout: 1500 } }
            - { type: temperature-sensor, name: u_vstupu, mcp980x: { node: patro.koupelna.u_vstupu, interval: 30 } }

        chodba:
            - { type: light, name: hlavni.tmp, relay: { node: prizemi.technicka.rmb_351, pin: 3, initial_state: true } }
            - { type: light, name: led, relay: { node: prizemi.technicka.rmb_352, pin: 2 } } # na PIR
            - { type: button, name: u_vstupu.levy, location: { node: patro.chodba.u_vstupu, pin: 3 } } # hlavni, dlouhý stisk LED nika
            - { type: button, name: u_vstupu.pravy, location: { node: patro.chodba.u_vstupu, pin: 2 } } # prizemi.hlavni
            - { type: button, name: u_koupelny.levy, location: { node: patro.chodba.u_koupelny, pin: 3 } } # hlavni
            - { type: button, name: u_pokoje.levy, location: { node: patro.chodba.u_pokoje, pin: 3 } } # hlavni
            # PIR u pracovny rozsvítí schodiste led prizemi.chodba.schodiste
            # PIR u koupelny rozsvítí led na minutu
            - { type: button, name: detekce_pohybu_u_loznice, location: { node: prizemi.technicka.rmb_352, pin: 7 } }    # 2 patro mezi koupelnou a ložnicí PIR 2.1.2
            - { type: button, name: detekce_pohybu_u_pracovny, location: { node: prizemi.technicka.rmb_353, pin: 6 } }    # nad schody u pracovny 2 patro PIR 2.1.1
            - { type: button, name: detekce_eps, location: { node: prizemi.technicka.rmb_352, pin: 6 } }    # horní EPS

        puda:
            - { type: light, name: hlavni, relay: { node: prizemi.technicka.rmb_357, pin: 3 } }
            - { type: button, name: u_vstupu.levy, location: { node: patro.puda.u_vstupu, pin: 3 } }
