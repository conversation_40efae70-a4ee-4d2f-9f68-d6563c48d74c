import asyncio

from caspia.pan import get_global_pan
from pan.integrations.notificator import TelegramNotificator
import os

pan = get_global_pan()
door = pan.lookup.door
telegram = TelegramNotificator(os.environ.get('CSP_TELEGRAM_GROUP_ID'))


windows_all_names = [
    'prizemi.pracovna.okno',
    'prizemi.obyvak.okno',
    'prizemi.kuchyn.okno',
    'prizemi.pokoj_vpravo.okno',
    'prizemi.technicka.okno',
    'prizemi.koupln.okno.leve',
    'prizemi.koupln.okno.okno.prave',
]

doors_all_names = [
    'prizemi.obyvak.dvere',
    'prizemi.loznice.dvere',
    'prizemi.zadveri.dvere',
    'prizemi.pokoj_vlevo.dvere',
]


async def is_doors_open():
    for name in doors_all_names:
        if await door(name).is_open.observe():
            return True
    return False


async def is_windows_open():
    for name in windows_all_names:
        if await door(name).is_open.observe():
            return True
    return False


@pan.builder.create_switch_with_action('switch.notif.otevreni.okna')
async def notif_otevreni_oken():
    if await is_windows_open():
        await telegram.raise_notification('⚠️ Je otevřené některé okno 🪟')
    else:
        await telegram.raise_notification('✅ Zavřená všechna okna 🪟')

    if await is_doors_open():
        await telegram.raise_notification('⚠️ Jsou otevřené některé dveře 🚪')
    else:
        await telegram.raise_notification('✅ Zavřeny všechny dveře 🚪')


async def check_otevreni_oken():
    if await is_windows_open():
        await telegram.raise_notification('⚠️ Je otevřené některé okno 🪟')


async def check_otevreni_dveri():
    if await is_doors_open():
        await telegram.raise_notification('⚠️ Jsou otevřené některé dveře 🚪')


async def notif_odchodove_tlacitko():
    await asyncio.sleep(60)
    await notif_otevreni_oken()
