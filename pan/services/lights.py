from caspia.pan import get_global_pan

pan = get_global_pan()
light = pan.lookup.light

# všechna světla
pan.builder.create_lightgroup('dum.group.vse').add(
    light('patro.chodba.hlavni'),
    light('patro.chodba.led'),
    light('patro.detsky_pokoj.hlavni'),
    light('patro.detsky_pokoj.u_okna'),
    light('patro.koupelna.hlavni'),
    light('patro.koupelna.wc'),
    light('patro.loznice.hlavni'),
    light('patro.pracovna.hlavni'),
    light('patro.puda.hlavni'),
    light('prizemi.chodba.hlavni'),
    light('prizemi.chodba.led'),
    light('prizemi.chodba.schody'),
    light('prizemi.koupelna.hlavni'),
    light('prizemi.kuchyn.hlavni'),
    light('prizemi.loznice.hlavni'),
    light('prizemi.obyvak.K1'),
    light('prizemi.obyvak.K2'),
    light('prizemi.obyvak.K3'),
    light('prizemi.obyvak.K4'),
    light('prizemi.technicka.hlavni'),
    light('prizemi.zadveri.hlavni')
)

pan.builder.create_lightgroup('patro.koupelna.group.vse').add(
    light('patro.koupelna.hlavni'),
    light('patro.koupelna.wc')
)

pan.builder.create_lightgroup('prizemi.koupelna.group.vse').add(
    light('prizemi.koupelna.hlavni'),
    light('prizemi.koupelna.wc')
)

pan.builder.create_lightgroup('prizemi.chodba.group.vse').add(
    light('prizemi.chodba.1.tmp'),
    light('prizemi.chodba.2.tmp')
)

pan.builder.create_lightgroup('prizemi.kuchyn.hlavni').add(
    light('prizemi.kuchyn.hlavni.1.tmp'),
    light('prizemi.kuchyn.hlavni.2.tmp')
)

pan.builder.create_lightgroup('prizemi.chodba.hlavni').add(
    light('prizemi.chodba.1.tmp'),
    light('prizemi.chodba.2.tmp')
)

pan.builder.create_lightgroup('patro.chodba.hlavni').add(
    light('patro.chodba.1.tmp'),
    light('patro.chodba.2.tmp'),
    light('patro.chodba.3.tmp')
)

pan.builder.create_lightgroup('prizemi.koupelna.hlavni').add(
    light('prizemi.koupelna.hlavni.1.tmp'),
    light('prizemi.koupelna.hlavni.2.tmp'),
    light('prizemi.koupelna.hlavni.3.tmp')
)

pan.builder.create_lightgroup('patro.koupelna.hlavni').add(
    light('patro.koupelna.1.tmp'),
    light('patro.koupelna.2.tmp'),
    light('patro.koupelna.3.tmp')
)

pan.builder.create_lightgroup('patro.loznice.hlavni').add(
    light('patro.loznice.hlavni.1.tmp'),
    light('patro.loznice.hlavni.2.tmp')
)
