import asyncio
import os
from caspia.pan import get_global_pan
from caspia.pan import reactive as r
from pan.utils import (blinds_tilt_all, blinds_blind_all, blinds_calibrate)
from pan.automation.reactive import (venku_fouka2, venku_fouka1, setmelo_se)
from pan.integrations.notificator import TelegramNotificator


pan = get_global_pan()
automator = pan.automator
namespace = pan.lookup.namespace
button = pan.lookup.button
blinds = pan.lookup.blinds
light = pan.lookup.light
blindsControl = pan.lookup['blinds-control']
telegram = TelegramNotificator(os.environ.get('CSP_TELEGRAM_GROUP_ID'))

venku_fouka1_notify = r.Value(True)
venku_fouka2_notify = r.Value(True)
nekdo_je_doma = r.someone_present()
nikdo_neni_doma = r.everyone_away()
setmelo_se_a_nikdo_neni_doma = setmelo_se & nikdo_neni_doma
setmelo_se_a_nikdo_neni_doma_a_venku_nefouka = setmelo_se_a_nikdo_neni_doma & (venku_fouka1 == False)
setmelo_se_a_nekdo_je_doma = setmelo_se & nekdo_je_doma
setmelo_se_a_nekdo_je_doma_a_venku_nefouka = setmelo_se_a_nekdo_je_doma & (venku_fouka1 == False)


@automator.create_automation('blinds: patro.detsky_pokoj.vpravo')
@automator.when(blinds('patro.detsky_pokoj.vpravo').movement == 'steady')
async def zaluzie_movement_detsky_pokoj():
    position = await blinds('patro.detsky_pokoj.vpravo').target_position.read()
    #logger.info('blinds: TEST: pokus s pohybem zaluzii patro.pracovna.vlevo')
    await asyncio.sleep(0.2)
    await blinds('patro.detsky_pokoj.stred').target_position.write(position)
    #await asyncio.sleep(0.2)
    #await blinds('patro.detsky_pokoj.vlevo').target_position.write(position)


#
# # Ochrana proti větru
# @automator.create_automation('Blinds: venku fouká, žaluzie horizontálně')
# @automator.when(r.transition(venku_fouka1 & r.everyone_away() & venku_fouka1_notify) == (False, True))
# async def hodne_fouka_zaluzie_natocit():
#     await telegram.raise_notification('💨 Venku fouká, žaluzie se natáčí do horizontální polohy')
#     await blinds_tilt_all(0.0)
#     await venku_fouka1_notify.update(False)
#
#
# @automator.create_automation('Blinds: venku fouká a někdo je doma')
# @automator.when(r.transition(venku_fouka1 & r.someone_present() & venku_fouka1_notify) == (False, True))
# async def hodne_fouka_notifikace_1():
#     await telegram.raise_notification('💨 Venku fouká. Pokud máte zavřené žaluzie, doporučuji je pootevřít.')
#     await venku_fouka1_notify.update(False)
#
#
# @automator.create_automation('Blinds: venku hodně fouká, žluzie vytáhnout')
# @automator.when(r.transition(venku_fouka2 & r.everyone_away() & venku_fouka2_notify) == (False, True))
# async def hodne_fouka_zaluzie_otevrit():
#     await telegram.raise_notification('💨 Venku hodně fouká, žaluzie se vytahují nahoru')
#     await blinds_blind_all(0.0)
#     await venku_fouka2_notify.update(False)
#
#
# @automator.create_automation('Blinds: venku hodně fouká a někdo je doma')
# @automator.when(r.transition(venku_fouka2 & r.someone_present() & venku_fouka2_notify) == (False, True))
# async def hodne_fouka_notifikace_2():
#     await telegram.raise_notification('💨 Venku hodně fouká. Pokud máte zatažené žaluzie, doporučuji je otevřít, nejlépe vytáhnout nahoru, aby se předešlo jejich poškození.')
#     await venku_fouka2_notify.update(False)
#
#
# @automator.create_automation('Blinds: žaluzie po setmění když nikdo není doma zatáhnout')
# @automator.when(setmelo_se_a_nikdo_neni_doma_a_venku_nefouka)
# async def setmelo_se_zaluzie_nepritomnost():
#     await blinds_blind_all(1.0)
#     await blinds_tilt_all(90.0)
#     await asyncio.sleep(1)
#     await blinds_calibrate()
#
#
# @automator.create_automation('Blinds: žaluzie po setmění když je někdo doma zatáhnout')
# @automator.when(setmelo_se_a_nekdo_je_doma_a_venku_nefouka)
# async def setmelo_se_zaluzie_pritomnost():
#     await blinds_blind_all(1.0)
#     await blinds_tilt_all(90.0)
#     await asyncio.sleep(1)
#     await blinds_calibrate()
#
#
# @automator.create_automation('Blinds: notifikace jednou denně')
# @automator.when(r.time_is('6:00'))
# async def blinds_spusteni_notifikaci_jednou_denne():
#     await venku_fouka1_notify.update(True)
#     await venku_fouka2_notify.update(True)
