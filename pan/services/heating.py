from caspia.meadow.services import ThermostatState
from caspia.pan import get_global_pan
from caspia.pan import reactive as r
from caspia.reactive import Observer
from caspia.toolbox.reactive.record import record as record_observable

pan = get_global_pan()
automator = pan.automator
temp = pan.lookup['temperature-sensor']
outlet = pan.lookup.outlet
thermostat = pan.lookup.thermostat

class WaterHeating(Observer):
    def __init__(self, heating):
        super().__init__()
        self.heating = heating

    async def on_next(self, value, target_temp, **kwargs):
        if value == None:
            await self.heating.is_on.write(False)
            return ThermostatState.OFF
        elif value > 0.025:
            await self.heating.is_on.write(True)
            return ThermostatState.HEATING
        elif value < -0.025:
            await self.heating.is_on.write(False)
            return ThermostatState.OFF

    async def on_error(self, error, **kwargs):
        pass

# filtrování proměnných
# zaok<PERSON><PERSON>ený lowpass
teplota_prizemi_koupelna_lowpass = r.lambda_(lambda t: round(t, 2), r.lowpass(
    temp('prizemi.koupelna.u_vstupu').temp, window=60*5, algorithm='median'))

teplota_prizemi_loznice_lowpass = r.lambda_(lambda t: round(t, 2), r.lowpass(
    temp('prizemi.loznice.u_vstupu').temp, window=60*5, algorithm='median'))

teplota_prizemi_kuchyn_lowpass = r.lambda_(lambda t: round(t, 2), r.lowpass(
    temp('prizemi.kuchyn.drez').temp, window=60*5, algorithm='median'))

teplota_prizemi_obyvak_lowpass = r.lambda_(lambda t: round(t, 2), r.lowpass(
    temp('prizemi.obyvak.u_okna2').temp, window=60*5, algorithm='median'))

teplota_patro_loznice_lowpass = r.lambda_(lambda t: round(t, 2), r.lowpass(
    temp('patro.loznice.u_vstupu').temp, window=60*5, algorithm='median'))

teplota_patro_detsky_pokoj_lowpass = r.lambda_(lambda t: round(t, 2), r.lowpass(
    temp('patro.detsky_pokoj.za_rohem').temp, window=60*5, algorithm='median'))

teplota_patro_pracovna_lowpass = r.lambda_(lambda t: round(t, 2), r.lowpass(
    temp('patro.pracovna.u_vstupu').temp, window=60*5, algorithm='median'))

teplota_patro_koupelna_lowpass = r.lambda_(lambda t: round(t, 2), r.lowpass(
    temp('patro.koupelna.u_vstupu').temp, window=60*5, algorithm='median'))




# topení koupelna
pan.automator.create_thermostat('prizemi.koupelna.termostat',
                                temperature=teplota_prizemi_koupelna_lowpass,
                                heating=WaterHeating(
                                    outlet('prizemi.koupelna.topeni')),
                                params=dict(p=1.0, sample_time=60*2))

# topení ložnice přízemí
pan.automator.create_thermostat('prizemi.loznice.termostat',
                                temperature=teplota_prizemi_loznice_lowpass,
                                heating=WaterHeating(
                                    outlet('prizemi.loznice.topeni')),
                                params=dict(p=1.0, sample_time=60*2))

# topení kuchyň přízemí
pan.automator.create_thermostat('prizemi.kuchyn.termostat',
                                temperature=teplota_prizemi_kuchyn_lowpass,
                                heating=WaterHeating(
                                    outlet('prizemi.kuchyn.topeni')),
                                params=dict(p=1.0, sample_time=60*2))

# TODO sauna a chodba nemají teplotní čidlo