import caspia.pan.reactive as r
from caspia.pan import get_global_pan
from pathlib import Path
from caspia.toolbox.integrations.weather_kit.weather_provider import WeatherKitProvider, WeatherKitProviderSettings, WeatherKitTokenProvider
# from caspia.toolbox.integrations.darksky import DarkSkyWeatherProvider
from caspia.toolbox.services.presence import Presence
from caspia.toolbox.services.sun import Sun
from caspia.toolbox.services.weather import Weather

pan = get_global_pan()

pritomnost = Presence('dum.pritomnost', users=['jakub', 'maja'], loop=pan.loop)
pan.gateway.add(pritomnost)
r.register_presence(pritomnost)

sun = Sun('ostatni.slunce', ('Vsetin', 'Czech Republic', "49°18'N", "17°58'E", 'Europe/Vienna', 389), loop=pan.loop)
pan.gateway.add(sun)
r.register_sun(sun)


weather = Weather('ostatni.pocasi', loop=pan.loop)
pan.gateway.add(weather)
r.register_weather(weather)

weather_provider = WeatherKitProvider(token_provider=WeatherKitTokenProvider(
    private_key_path=Path(__file__).parent / 'AuthKey_8CZ5SJ5526.p8',
    app_id='org.caspiatech.home.app',
    team_id='T3DJ4L8TR5',
    key_id='8CZ5SJ5526'),
    settings=WeatherKitProviderSettings(49.3062925, 17.9618422),
    weather_service=weather)
