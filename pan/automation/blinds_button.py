from caspia.pan import get_global_pan

from ..utils import (BlindsMode, blinds_control, blinds_obyvak_blind_all, blinds_obyvak_tilt_all, blinds_obyvak_blind_mode)

pan = get_global_pan()
namespace = pan.lookup.namespace
button = pan.lookup.button
blinds = pan.lookup.blinds
blindsControl = pan.lookup['blinds-control']


with namespace('prizemi.koupelna'):
    blinds_control(
        button_up=button('.u_vstupu.nahoru'),
        button_down=button('.u_vstupu.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )

with namespace('prizemi.loznice'):
    blinds_control(
        button_up=button('.u_vstupu.nahoru'),
        button_down=button('.u_vstupu.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )
    blinds_control(
        button_up=button('.postel_vlevo.nahoru'),
        button_down=button('.postel_vlevo.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )
    blinds_control(
        button_up=button('.postel_vpravo.nahoru'),
        button_down=button('.postel_vpravo.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )

async def zaluzie_obyvak_nahoru():
    # žaluzie block
    await blinds_obyvak_blind_mode(BlindsMode.block)

    # žaluzie horizontálně
    #await blinds_obyvak_blind_all(1.0)
    #await blinds_obyvak_tilt_all(-10.0)


async def zaluzie_obyvak_dolu():
    # žaluzie zavrit
    await blinds_obyvak_blind_all(1.0)
    await blinds_obyvak_tilt_all(90.0)


with namespace('prizemi.obyvak'):
    button('.u_vstupu.nahoru').on('click').do(zaluzie_obyvak_nahoru)
    button('.u_vstupu.dolu').on('click').do(zaluzie_obyvak_dolu)
    # blinds_control(
    #     button_up=button('.u_vstupu.nahoru'),
    #     button_down=button('.u_vstupu.dolu'),
    #     blinds=blinds('.hlavni'),
    #     blinds_ctl=blindsControl('.hlavni')
    # )
    blinds_control(
        button_up=button('.u_okna1.vlevo_nahoru'),
        button_down=button('.u_okna1.vlevo_dolu'),
        blinds=blinds('.fix'),
        blinds_ctl=blindsControl('.fix')
    )
    blinds_control(
        button_up=button('.u_okna1.vpravo_nahoru'),
        button_down=button('.u_okna1.vpravo_dolu'),
        blinds=blinds('.vpravo'),
        blinds_ctl=blindsControl('.vpravo')
    )

with namespace('prizemi.kuchyn'):
    blinds_control(
        button_up=button('.u_terasy1.vlevo_nahoru'),
        button_down=button('.u_terasy1.vlevo_dolu'),
        blinds=blinds('..obyvak.terasa_vstup'),
        blinds_ctl=blindsControl('..obyvak.terasa_vstup')
    )
    blinds_control(
        button_up=button('.u_terasy1.vpravo_nahoru'),
        button_down=button('.u_terasy1.vpravo_dolu'),
        blinds=blinds('..obyvak.terasa_fix'),
        blinds_ctl=blindsControl('..obyvak.terasa_fix')
    )
    blinds_control(
        button_up=button('.drez_vpravo.nahoru'),
        button_down=button('.drez_vpravo.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )

with namespace('patro.loznice'):
    blinds_control(
        button_up=button('.u_vstupu.nahoru'),
        button_down=button('.u_vstupu.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )
    blinds_control(
        button_up=button('.postel_vlevo.nahoru'),
        button_down=button('.postel_vlevo.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )
    blinds_control(
        button_up=button('.postel_vpravo.nahoru'),
        button_down=button('.postel_vpravo.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )

with namespace('patro.detsky_pokoj'):
    blinds_control(
        button_up=button('.u_vstupu.nahoru'),
        button_down=button('.u_vstupu.dolu'),
        blinds=blinds('.vpravo'),
        blinds_ctl=blindsControl('.vpravo') # TODO + ovládat i střed
    )
    blinds_control(
        button_up=button('.za_rohem.nahoru'),
        button_down=button('.za_rohem.dolu'),
        blinds=blinds('.vlevo'),
        blinds_ctl=blindsControl('.vlevo')
    )
    blinds_control(
        button_up=button('.u_okna.nahoru'),
        button_down=button('.u_okna.dolu'),
        blinds=blinds('.vlevo'),
        blinds_ctl=blindsControl('.vlevo')
    )

with namespace('patro.pracovna'):
    blinds_control(
        button_up=button('.u_vstupu.nahoru'),
        button_down=button('.u_vstupu.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )

with namespace('patro.koupelna'):
    blinds_control(
        button_up=button('.u_vstupu.nahoru'),
        button_down=button('.u_vstupu.dolu'),
        blinds=blinds('.hlavni'),
        blinds_ctl=blindsControl('.hlavni')
    )