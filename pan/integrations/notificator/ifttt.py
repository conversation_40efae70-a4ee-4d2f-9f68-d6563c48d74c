import os

from .base import NotificatorBase

class IFTTTNotificator(NotificatorBase):

    def __init__(self, event_type="Message", *args, **kwargs):
        super().__init__(*args, **kwargs)        
        self.event_type = event_type
        self.access_token = os.environ.get("CSP_IFTTT_ACCESS_TOKEN", "")

        self.message_endpoint = f"https://maker.ifttt.com/trigger/{self.event_type}/with/key/{self.access_token}"
              
    async def raise_notification(self, message):
        payload = {
            "value1": message
        }

        async with self._session.post(self.message_endpoint, json=payload) as response:
            status = response.status
            content = await response.text()
            
            self._logger.info(f"[{status}] Response from Telegram Message Endpoint: {content}")